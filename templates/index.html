<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文档转Markdown服务</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h2 {
            font-size: 22px;
            margin-bottom: 10px;
        }

        h3 {
            font-size: 18px;
            margin: 10px 0;
        }
        .upload-container {
            border: 2px dashed #ccc;
            padding: 5px;
            text-align: center;
            margin-bottom: 5px;
        }
        #result {
            margin-top: 10px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            display: none;
        }
        #markdown-content {
            max-height: 400px;
            overflow-y: auto;
            padding: 10px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .markdown-body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, <PERSON>l, sans-serif;
            line-height: 1.5;
            color: #24292e;
            word-break: break-word;
            white-space: normal;
        }
        .markdown-body pre {
            background-color: #f6f8fa;
            border-radius: 3px;
            padding: 16px;
            white-space: pre-wrap;
            word-break: break-word;
            overflow-wrap: anywhere;
        }
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255,255,255,0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            flex-direction: column;
        }
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(0,0,0,0.1);
            border-radius: 50%;
            border-top-color: #4CAF50;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        .loading-text {
            margin-top: 16px;
            font-size: 16px;
            color: #333;
            font-weight: bold;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            cursor: pointer;
            margin-top: 10px;
            display: inline-block;
            font-size: 14px;
            text-align: center;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        button:hover {
            background-color: #45a049;
        }
        /* 隐藏原始文件输入控件 */
        #fileInput {
            display: none;
        }

        /* 伪按钮样式，使用 label 实现点击上传 */
        .upload-btn {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            cursor: pointer;
            margin-top: 10px;
            display: inline-block;
            font-size: 14px;
            text-align: center;
            border-radius: 4px;
            transition: background-color 0.2s;
        }

        .upload-btn:hover {
            background-color: #45a049;
        }
        #fileNames {
            margin-top: 10px;
            font-size: 14px;
            color: #333;
        }
        .upload-row {
            display: flex;
            justify-content: center; /* 横向居中 */
            align-items: center;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap; /* 超出自动换行 */
        }
        .markdown-body table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 10px;
        }

        .markdown-body th,
        .markdown-body td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }

        .markdown-body th {
            background-color: #f2f2f2;
        }
    </style>
    <script>
        window.MathJax = {
          tex: {
            inlineMath: [['$', '$'], ['\\(', '\\)']],
            displayMath: [['$$', '$$']],
            processEscapes: true
          },
          svg: {
            fontCache: 'global'
          }
        };
    </script>
    <script src="mineru/js/tex-svg.js"></script>
</head>
<body>
    <div id="loadingOverlay" class="loading-overlay" style="display: none;">
        <div style="text-align: center;">
            <div class="loading-spinner"></div>
            <div class="loading-text">处理中，请稍候...</div>
        </div>
    </div>
    <h2>文档转Markdown服务</h2>
    <p style="margin-top: 5px; color: #555;">
        本服务支持将上传的文档转换为带有格式的 Markdown 文件，支持标题分级、表格识别、公式转换（可转为 LaTeX 格式）、图片内容识别等信息，适用于知识整理、文档归档等场景。
    </p>
    <div class="upload-container">
        <h3>上传文件</h3>
        <div style="font-size: 13px; color: #666;">支持文件格式：pdf, docx, doc, pptx, ppt, jpg, jpeg, png</div>
        <form id="uploadForm">
            <div style="margin-bottom: 15px;">
                <label for="modelSelect">选择图片处理模型:</label>
                <select id="modelSelect" style="padding: 8px; border-radius: 4px; border: 1px solid #ccc; min-width: 300px;">
                    <option value="ocr">OCR模型 (速度快，仅识别文字)</option>
                    <option value="vl">VL模型 (速度慢，可描述图片内容)</option>
                </select>
            </div>
            <div style="margin-bottom: 15px;">
                <label for="formula_enable">选择是否识别公式:</label>
                <select id="formula_enable" style="padding: 8px; border-radius: 4px; border: 1px solid #ccc; min-width: 300px;">
                    <option value="false">否 (速度快，跳过公式)</option>
                    <option value="true">是 (速度慢，可将公式转为LateX格式)</option>
                </select>
            </div>
            <div class="upload-row">
                <label for="fileInput" class="upload-btn">选择文件</label>
                <div id="fileNames">未选择文件</div>
                <button type="submit">上传并处理</button>
                <button type="button" id="downloadSampleBtn">下载示例文件</button>
            </div>
            <input type="file" id="fileInput"
                accept=".pdf,.docx,.doc,.pptx,.ppt,.jpeg,.png,.jpg">
        </form>
    </div>

    <div id="result">
        <div style="font-weight: bold;">处理结果：</div>
        <div id="status"></div>
        <div id="markdown-content"></div>
        <button id="downloadBtn">下载Markdown文件</button>
    </div>

    <script src="mineru/js/marked.min.js"></script>
    <script>
        const fileInput = document.getElementById('fileInput');
        const fileNamesDiv = document.getElementById('fileNames');

        fileInput.addEventListener('change', function () {
            if (fileInput.files.length === 0) {
            fileNamesDiv.textContent = '未选择文件';
            } else {
            const names = Array.from(fileInput.files).map(f => f.name);
            fileNamesDiv.innerHTML = names.join('<br>');
            }
        });

        // 下载实例文件按钮点击事件
        document.getElementById('downloadSampleBtn').addEventListener('click', function() {
            // 创建一个隐藏的链接来触发下载
            const link = document.createElement('a');
            link.href = '/mineru/download-sample';
            link.download = 'simple.pdf';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        });

        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            const statusDiv = document.getElementById('status');

            // 清空之前的状态
            statusDiv.textContent = '';
            statusDiv.style.color = 'red';

            // 验证文件 - 更早更明显的提示
            if (!fileInput.files || fileInput.files.length === 0) {
                alert('错误：请先选择要上传的文件');
                return;
            }

            // 验证文件类型
            const allowedTypes = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'application/msword', 'application/vnd.ms-powerpoint', 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                                'image/jpeg', 'image/png'];
            if (!allowedTypes.includes(file.type)) {
                alert('错误：请上传PDF、Word、PPT或图片文件');
                return;
            }

            // 显示加载状态
            document.getElementById('loadingOverlay').style.display = 'flex';
            document.getElementById('uploadForm').querySelector('button[type="submit"]').disabled = true;

            const formData = new FormData();
            const vl_type = document.getElementById('modelSelect').value;
            const formula_enable = document.getElementById('formula_enable').value;

            file.name = file.name + Math.random();
            formData.append('file', file);
            formData.append('vl_type', vl_type);
            formData.append('formula_enable', formula_enable);

            try {
                const response = await fetch('/mineru/upload', {
                    method: 'POST',
                    body: formData
                });
                if (response.status == 503) {
                    throw new Error('当前排队人数过多，请稍后再试');
                }
                if (response.status !== 200) {
                    throw new Error(result.error || '处理失败');
                }

                const result = await response.json();

                document.getElementById('status').textContent = '处理成功！';
                document.getElementById('markdown-content').innerHTML =
                    '<div class="markdown-body">' + marked.parse(result.markdown) + '</div>';
                // ✅ 触发 MathJax 渲染
                if (window.MathJax && MathJax.typesetPromise) {
                    MathJax.typesetPromise(["#markdown-content"]);
                }
                // 设置下载按钮点击事件
                document.getElementById('downloadBtn').onclick = function() {
                    const markdownContent = result.markdown;
                    const blob = new Blob([markdownContent], {type: 'text/markdown'});
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;

                    // 获取原始文件名并处理
                    const originalFile = document.getElementById('fileInput').files[0];
                    let fileName = originalFile.name;
                    // 移除原扩展名并添加.md
                    fileName = fileName.replace(/\.[^/.]+$/, "") + '.md';
                    // 仅替换路径分隔符等危险字符
                    fileName = fileName.replace(/[\/\\\?\*\|":<>]/g, '_');

                    a.download = fileName;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    URL.revokeObjectURL(url);
                };
                document.getElementById('result').style.display = 'block';
            } catch (error) {
               document.getElementById('status').textContent = '错误: ' + error.message;
               document.getElementById('result').style.display = 'block';
           } finally {
               // 隐藏加载状态
               document.getElementById('loadingOverlay').style.display = 'none';
               document.getElementById('uploadForm').querySelector('button[type="submit"]').disabled = false;
           }
        });
    </script>
</body>
</html>